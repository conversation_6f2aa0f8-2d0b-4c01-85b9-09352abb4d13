{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1.药品知识回答"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.加载模型制作训练集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from PIL import Image\n", "import json\n", "from modelscope import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor\n", "from qwen_vl_utils import process_vision_info\n", "import torch\n", "\n", "base_dir = '~/模块C/resource/药品信息'\n", "resized_dir = '~/模块C/resource/药品信息/resized'\n", "model_dir = \"~/模块C/resource/Qwen2-VL-2B-Instruct\"\n", "os.makedirs(resized_dir, exist_ok=True)\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype=\"auto\", device_map=\"auto\")\n", "processor = AutoProcessor.from_pretrained(model_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> resize函数：将图片 resize 到合适大小 两处代码共1.5分\n", "def resize_image(input_path, output_path, max_size=____):\n", "    try:\n", "        img = Image.open(input_path)\n", "        _____________________________\n", "        _____________________________\n", "        img.save(output_path, format='JPEG')\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Failed to process {input_path}: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = []\n", "\n", "# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分\n", "for sub in os.listdir(base_dir):\n", "    sub_path = os.path.join(base_dir, sub)\n", "    if not os.path.isdir(sub_path):\n", "        continue\n", "\n", "    imgs = [fname for fname in sorted(os.listdir(sub_path))\n", "            if fname.lower().endswith(('.jpeg'))]\n", "    if not imgs:\n", "        continue\n", "\n", "    front_candidates = ________\n", "    front_name = None\n", "    for name in imgs:\n", "        if name in front_candidates:\n", "            front_name = name\n", "            break\n", "    if front_name is None:\n", "        front_name = imgs[0]\n", "\n", "    resized_paths_all = []\n", "    resized_front_path = None\n", "\n", "    for name in imgs:\n", "        orig_path = os.path.join(sub_path, name)\n", "        out_sub_dir = os.path.join(resized_dir, sub)\n", "        os.makedirs(out_sub_dir, exist_ok=True)\n", "        out_path = os.path.join(out_sub_dir, name)\n", "        success = ________________________________\n", "        if success:\n", "            uri = f\"file://{out_path}\"\n", "            resized_paths_all.append(uri)\n", "            if name == front_name:\n", "                resized_front_path = uri\n", "\n", "    if resized_front_path is None:\n", "        print(f\"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过\")\n", "        continue\n", "\n", "# <3> 构造 messages，模型多图推理 4处代码共2分\n", "\n", "    user_instruction = _________________________________________________________\n", "    content = []\n", "    for p in resized_paths_all:\n", "        content.append(________________________________)\n", "    content.append({\"type\": \"text\", \"text\": user_instruction})\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": content\n", "        }\n", "    ]\n", "    \n", "    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n", "    image_inputs, video_inputs = process_vision_info(messages)\n", "    inputs = processor(\n", "        text=[text],\n", "        images=image_inputs,\n", "        videos=video_inputs,\n", "        padding=True,\n", "        return_tensors=\"pt\"\n", "    )\n", "    inputs = inputs.to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        generated_ids = model.generate(**inputs, max_new_tokens=_____)\n", "    \n", "    generated_ids_trimmed = [\n", "        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "    ]\n", "    output_texts = processor.batch_decode(\n", "        generated_ids_trimmed,\n", "        skip_special_tokens=True,\n", "        clean_up_tokenization_spaces=False\n", "    )\n", "    answer = _______________________\n", "\n", "# <4> 构造并保存训练集，3处代码共1.5分\n", "    entry = {\n", "        \"conversations\": [\n", "            {\"from\": \"human\", \"value\": user_instruction},\n", "            {\"from\": \"gpt\", \"value\": answer}\n", "        ],\n", "        \"images\": ____________________\n", "    }\n", "    train_data.append(entry)\n", "\n", "\n", "output_json_path = _______________________\n", "with open(output_json_path, 'w', encoding='utf-8') as f:\n", "    json.dump(_______________________)\n", "\n", "print(f\"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <5>  检查生成的药品信息，并在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。1分。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2.模型训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <6>  填充examples/train_lora/qwen2vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。\n", "\n", "### model\n", "model_name_or_path: ~/模块C/resource/Qwen2-VL-2B-Instruct\n", "image_max_pixels: ______\n", "trust_remote_code: true\n", "\n", "### method\n", "stage: sft\n", "do_train: true\n", "finetuning_type: lora\n", "lora_rank: 8\n", "lora_target: all\n", "\n", "### dataset\n", "dataset: _________ \n", "template: qwen2_vl\n", "cutoff_len: 2048\n", "max_samples: _________\n", "overwrite_cache: true\n", "preprocessing_num_workers: 16\n", "dataloader_num_workers: 4\n", "\n", "### output\n", "output_dir: ~/模块C/ans/sft_model\n", "logging_steps: 10\n", "save_steps: 500\n", "plot_loss: true\n", "overwrite_output_dir: true\n", "save_only_model: false\n", "report_to: none  \n", "\n", "### train\n", "per_device_train_batch_size: ___\n", "gradient_accumulation_steps: ___\n", "learning_rate: ___\n", "num_train_epochs: ___\n", "lr_scheduler_type: cosine\n", "warmup_ratio: 0.1\n", "bf16: true\n", "ddp_timeout: 180000000\n", "resume_from_checkpoint: null\n", "\n", "### eval\n", "# val_size: 0.1\n", "# per_device_eval_batch_size: 1\n", "# eval_strategy: steps\n", "# eval_steps: 500"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <7> 开始训练 llamafactory-cli train examples/train_lora/qwen2vl_lora_sft.yaml "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤3.模型导出"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# <8> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分\n", "\n", "### model\n", "model_name_or_path: ~/模块C/resource/Qwen2-VL-2B-Instruct\n", "adapter_name_or_path: ~/模块C/ans/sft_model\n", "template: qwen2_vl\n", "trust_remote_code: true\n", "\n", "### export\n", "export_dir: ~/模块C/ans/qwen2vl_lora_sft\n", "export_size: 5\n", "export_device: auto  \n", "export_legacy_format: false"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤4.模型能力评测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <9> 根据 Accuracy 及 规范输出数量 进行打分\n", "# 相同分数情况下可比较规范输出数量"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. 场景实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.模型转换、编译、部署、推理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <10> 参考 \"~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo\"文档进行模型转换和部署推理 共3分\n", "# 模型转换（rknn、rkllm转换成功 1分）\n", "# 模型编译 1分\n", "# 模型部署、推理 1分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2.板端测试"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <11> 每题0.5分 共2.5分"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 2}